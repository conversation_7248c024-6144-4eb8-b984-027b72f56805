{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIElementsNativeModule.dll", "UnityEngine.UNETModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Unity.Addressables.dll", "SelectableGroup.dll", "MoreMountains.Feedbacks.URP.dll", "Unity.InputSystem.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "UnityEngine.UI.dll", "DOTween.Modules.dll", "Unity.2D.IK.Runtime.dll", "UniTask.TextMeshPro.dll", "Cinemachine.dll", "MoreMountains.Feedbacks.NiceVibrations.dll", "MoreMountains.Feedbacks.PostProcessing.dll", "MeshBakerCore.dll", "ToonyColorsPro.Runtime.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "MK.<PERSON>.dll", "Unity.Toonshader.dll", "Unity.AI.Navigation.dll", "MoreMountains.Feedbacks.TextMeshPro.dll", "Beautify.Universal.Runtime.dll", "Unity.Collections.dll", "idbrii.NavGen.dll", "Lofelt.NiceVibrations.Demo.dll", "UniTask.Addressables.dll", "Unity.Recorder.dll", "MoreMountains.Feedbacks.Cinemachine.dll", "Unity.2D.Tilemap.Extras.dll", "PaintIn3D.dll", "MoreMountains.Feedbacks.HDRP.dll", "ToonyColorsPro2.Demo.dll", "Unity.Recorder.Base.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.Timeline.dll", "KinoBloom.Runtime.dll", "Unity.TextMeshPro.dll", "TileWorldCreator.dll", "MeshCombineStudio.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.ResourceManager.dll", "UniTask.DOTween.dll", "KinematicCharacterController.dll", "MagicaCloth.dll", "UniTask.Linq.dll", "MoreMountains.Feedbacks.MMTools.dll", "Unity.Mathematics.dll", "UniRx.dll", "Elringus.Naninovel.Runtime.dll", "Unity.Burst.dll", "UIEffect.dll", "Unity.Jobs.dll", "Unity.2D.Animation.Runtime.dll", "MoreMountains.Feedbacks.dll", "Lofelt.NiceVibrations.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Unity.2D.Common.Runtime.dll", "Unity.ScriptableBuildPipeline.dll", "UniTask.dll", "MoreMountains.Tools.dll", "Unity.Collections.LowLevel.ILSupport.dll", "MeshExtension.dll", "Naninovel.Bridging.dll", "Sirenix.OdinInspector.Attributes.dll", "Naninovel.NCalc.dll", "Newtonsoft.Json.dll", "Sirenix.Serialization.Config.dll", "DOTween.dll", "StateAttribute.dll", "Naninovel.Parsing.dll", "XInputDotNetPure.dll", "Facepunch.Steamworks.Win64.dll", "Unity.Burst.Unsafe.dll", "NLayer.dll", "Sirenix.Serialization.dll", "Sirenix.Utilities.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}