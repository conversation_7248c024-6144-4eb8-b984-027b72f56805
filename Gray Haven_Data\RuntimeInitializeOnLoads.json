{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "Game", "methodName": "InitUniTaskLoop", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "Game", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "Game", "methodName": "LoadDefaultScene", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "TimeManager", "methodName": "Init", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "VolumetricFogAndMist2", "className": "VolumetricFog", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem.Interactions", "className": "CustomHoldInteraction", "methodName": "RegisterInteraction", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine.PostFX", "className": "CinemachineVolumeSettings", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Universal.Runtime", "nameSpace": "UnityEngine.Rendering.Universal", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "MagicaPhysicsManager", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "MagicaPhysicsManager", "methodName": "InitCustomGameLoop", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "SimpleInputManager", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "SceneBackground", "methodName": "ResetStaticData", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "SyncContextUtils", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "ConsoleGUI", "methodName": "Destroy", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "InputPreprocessor", "methodName": "ResetPreprocessor", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "ConsoleCommands", "methodName": "SetupDevelopmentConsole", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "Engine", "methodName": "CheckUnityVersion", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "RuntimeInitializer", "methodName": "DisposeTCS", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "<PERSON><PERSON><PERSON>", "className": "RuntimeInitializer", "methodName": "OnApplicationLoaded", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "Naninovel.UI", "className": "CustomVariableGUI", "methodName": "ResetOnPlayMode", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Elringus.Naninovel.Runtime", "nameSpace": "Naninovel.Async", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "UIEffect", "nameSpace": "Coffee.UIEffects", "className": "GraphicConnector", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}]}