<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="ARPGFX.ARPGFXLightFlicker" preserve="all" />
    <type fullname="ARPGFX.ARPGFXRotation" preserve="all" />
    <type fullname="AttackRangeSquare" preserve="all" />
    <type fullname="AutoDestroyPS" preserve="all" />
    <type fullname="AutoDoor" preserve="all" />
    <type fullname="Buff.EmotionController" preserve="all" />
    <type fullname="Buff.Memory.VespaShockWave" preserve="all" />
    <type fullname="ButtonGuidePanel" preserve="all" />
    <type fullname="Characters.AttackDirectionArrow" preserve="all" />
    <type fullname="Characters.AttackRangeCircle" preserve="all" />
    <type fullname="Characters.AttackRangeSliced" preserve="all" />
    <type fullname="Characters.Character" preserve="all" />
    <type fullname="Characters.KillAllChildren" preserve="all" />
    <type fullname="Characters.MotionTrail" preserve="all" />
    <type fullname="CheckPanel" preserve="all" />
    <type fullname="CheckPoint" preserve="all" />
    <type fullname="Data.AbnormalDataTable" preserve="all" />
    <type fullname="Data.BattleStyleDataTable" preserve="all" />
    <type fullname="Data.BuffControlDataTable" preserve="all" />
    <type fullname="Data.ClothsDataTable" preserve="all" />
    <type fullname="Data.CyberwareDataTable" preserve="all" />
    <type fullname="Data.DataBox" preserve="all" />
    <type fullname="Data.EquippableItemDataTable" preserve="all" />
    <type fullname="Data.FieldDataTable" preserve="all" />
    <type fullname="Data.FieldObjectDataTable" preserve="all" />
    <type fullname="Data.HousingDataTable" preserve="all" />
    <type fullname="Data.ItemDataTable" preserve="all" />
    <type fullname="Data.ItemDropDataTable" preserve="all" />
    <type fullname="Data.ItemShopDataTable" preserve="all" />
    <type fullname="Data.JournalDataTable" preserve="all" />
    <type fullname="Data.PopupDataTable" preserve="all" />
    <type fullname="Data.SmithDataTable" preserve="all" />
    <type fullname="Data.StatLevelDataTable" preserve="all" />
    <type fullname="Data.TranslationDataTable" preserve="all" />
    <type fullname="ECparticleColorChangerMaster" preserve="all" />
    <type fullname="Effects.Effect" preserve="all" />
    <type fullname="EGA_EffectSound" preserve="all" />
    <type fullname="Farming.PlantProcessor" preserve="all" />
    <type fullname="FieldObjects.ComponentAttacher" preserve="all" />
    <type fullname="FieldObjects.DungeonWall" preserve="all" />
    <type fullname="FieldObjects.EntranceFieldObject" preserve="all" />
    <type fullname="FieldObjects.ExitFieldObject" preserve="all" />
    <type fullname="FieldObjects.FieldObject" preserve="all" />
    <type fullname="FieldObjects.FieldObjectChanger" preserve="all" />
    <type fullname="FieldObjects.FieldObjectMetadata" preserve="all" />
    <type fullname="FieldObjects.ItemFieldObject" preserve="all" />
    <type fullname="FieldObjects.MonsterSummoner" preserve="all" />
    <type fullname="FireTrap" preserve="all" />
    <type fullname="FootstepBehaviour" preserve="all" />
    <type fullname="GameObjectToggleEvent" preserve="all" />
    <type fullname="HagenithFireTrap" preserve="all" />
    <type fullname="Hovl_Laser" preserve="all" />
    <type fullname="InventoryGuide" preserve="all" />
    <type fullname="Loading" preserve="all" />
    <type fullname="MeiDungeonAudio" preserve="all" />
    <type fullname="MonsterRegenHelper" preserve="all" />
    <type fullname="OffscreenMarker" preserve="all" />
    <type fullname="Players.SaveSlot" preserve="all" />
    <type fullname="PositionClip" preserve="all" />
    <type fullname="PositionTrack" preserve="all" />
    <type fullname="Projectiles.EllaPhase2CutShot" preserve="all" />
    <type fullname="Projectiles.EllaPhase2MadShot" preserve="all" />
    <type fullname="Projectiles.GauntletProjectile" preserve="all" />
    <type fullname="Projectiles.KukulProjectile" preserve="all" />
    <type fullname="Projectiles.LaserController" preserve="all" />
    <type fullname="Projectiles.Manshin.ManshinProjectileEnergyBeam" preserve="all" />
    <type fullname="Projectiles.Thunderstroke" preserve="all" />
    <type fullname="Projectiles.TrackingLaser" preserve="all" />
    <type fullname="Projectiles.TrackingMissile" preserve="all" />
    <type fullname="Quest.BeaconSupporter1" preserve="all" />
    <type fullname="Quest.BeaconSupporter2" preserve="all" />
    <type fullname="Quest.BeaconSupporter3" preserve="all" />
    <type fullname="Quest.BeaconSupporter4" preserve="all" />
    <type fullname="Quest.MeiQuestWarpSupporter" preserve="all" />
    <type fullname="Quest.NikolQuestWarpSupporter" preserve="all" />
    <type fullname="Quest.VcamController" preserve="all" />
    <type fullname="RainbowArt.PlayAnimHelper" preserve="all" />
    <type fullname="RandomFiveSpawner" preserve="all" />
    <type fullname="RandomSixSpawner" preserve="all" />
    <type fullname="RandomSpawner" preserve="all" />
    <type fullname="RandomWarpSupporter" preserve="all" />
    <type fullname="RecyclerList" preserve="all" />
    <type fullname="ScreenFaderClip" preserve="all" />
    <type fullname="ScreenFaderTrack" preserve="all" />
    <type fullname="SimpleRaycastTarget" preserve="all" />
    <type fullname="Stages.Area" preserve="all" />
    <type fullname="Stages.Field" preserve="all" />
    <type fullname="Stages.MonsterPool" preserve="all" />
    <type fullname="Stages.SpawnerData" preserve="all" />
    <type fullname="Stages.StageMetadata" preserve="all" />
    <type fullname="SubMapControl" preserve="all" />
    <type fullname="TextIndicator" preserve="all" />
    <type fullname="TGGameEngine.TGDoor" preserve="all" />
    <type fullname="Tileline.TimelineController" preserve="all" />
    <type fullname="Timer" preserve="all" />
    <type fullname="Trap.ActivateTrap" preserve="all" />
    <type fullname="Trap.BearTrap" preserve="all" />
    <type fullname="Trap.BlockTrap" preserve="all" />
    <type fullname="Trap.FloorTrap" preserve="all" />
    <type fullname="Trap.MoveFloor" preserve="all" />
    <type fullname="Trap.MoveWall" preserve="all" />
    <type fullname="Trap.MoveWallTrigger" preserve="all" />
    <type fullname="Trap.ObstacleSpawner" preserve="all" />
    <type fullname="Trap.SlowZone" preserve="all" />
    <type fullname="Trap.Trap_Spike" preserve="all" />
    <type fullname="Trap.Trap_SpikeCollider" preserve="all" />
    <type fullname="Trap.TrapController" preserve="all" />
    <type fullname="Trap.WallTrigger" preserve="all" />
    <type fullname="Triggers.ActiveWithEffect" preserve="all" />
    <type fullname="Triggers.FieldTrigger" preserve="all" />
    <type fullname="Triggers.MonsterHpTrigger" preserve="all" />
    <type fullname="Triggers.MonsterTrigger" preserve="all" />
    <type fullname="Triggers.SpawnPointTrigger" preserve="all" />
    <type fullname="TripleRandomWarpSupporter" preserve="all" />
    <type fullname="Tutorial02TextController" preserve="all" />
    <type fullname="TutorialControlTip" preserve="all" />
    <type fullname="TutorialTipController" preserve="all" />
    <type fullname="UI.AgenisIngredientMix" preserve="all" />
    <type fullname="UI.Buy" preserve="all" />
    <type fullname="UI.Cooking" preserve="all" />
    <type fullname="UI.CookingIcon" preserve="all" />
    <type fullname="UI.Cyberware" preserve="all" />
    <type fullname="UI.CyberwareIcon" preserve="all" />
    <type fullname="UI.CyberwareList" preserve="all" />
    <type fullname="UI.CyberwareManage" preserve="all" />
    <type fullname="UI.DaynaSpecial" preserve="all" />
    <type fullname="UI.Diy" preserve="all" />
    <type fullname="UI.DiyIcon" preserve="all" />
    <type fullname="UI.DiyList" preserve="all" />
    <type fullname="UI.DiyManage" preserve="all" />
    <type fullname="UI.EnhanceCenterIcon" preserve="all" />
    <type fullname="UI.EnhanceCenterLIst" preserve="all" />
    <type fullname="UI.EnhanceIcon" preserve="all" />
    <type fullname="UI.EnhanceList" preserve="all" />
    <type fullname="UI.Enhancing" preserve="all" />
    <type fullname="UI.HousingUpgrade_01" preserve="all" />
    <type fullname="UI.HousingUpgradeCenterPanel" preserve="all" />
    <type fullname="UI.HousingUpgradeIcon" preserve="all" />
    <type fullname="UI.HousingUpgradeList" preserve="all" />
    <type fullname="UI.Inventory" preserve="all" />
    <type fullname="UI.ItemIcon" preserve="all" />
    <type fullname="UI.ItemInfo" preserve="all" />
    <type fullname="UI.ItemList" preserve="all" />
    <type fullname="UI.Journal" preserve="all" />
    <type fullname="UI.JournalIcon" preserve="all" />
    <type fullname="UI.JournalInfo" preserve="all" />
    <type fullname="UI.JournalList" preserve="all" />
    <type fullname="UI.KukulCoinBuy" preserve="all" />
    <type fullname="UI.MemoryChapterButton" preserve="all" />
    <type fullname="UI.MemoryCutsceneList" preserve="all" />
    <type fullname="UI.MemoryCutSceneManager" preserve="all" />
    <type fullname="UI.MenuSlot" preserve="all" />
    <type fullname="UI.MixIcon" preserve="all" />
    <type fullname="UI.ModeSelectButton" preserve="all" />
    <type fullname="UI.MoveDestination" preserve="all" />
    <type fullname="UI.Panel.AgenisMix" preserve="all" />
    <type fullname="UI.Panel.AgenisPanel" preserve="all" />
    <type fullname="UI.Panel.AgenisStoragePanel" preserve="all" />
    <type fullname="UI.Panel.ArenaPanel" preserve="all" />
    <type fullname="UI.Panel.BuyKukulCoinPanel" preserve="all" />
    <type fullname="UI.Panel.BuyPanel" preserve="all" />
    <type fullname="UI.Panel.ChangeCharacterClothPanel" preserve="all" />
    <type fullname="UI.Panel.CharacterScenePanel" preserve="all" />
    <type fullname="UI.Panel.CommonNoticePanel" preserve="all" />
    <type fullname="UI.Panel.CommonPanel" preserve="all" />
    <type fullname="UI.Panel.CookingPanel" preserve="all" />
    <type fullname="UI.Panel.CreditPanel" preserve="all" />
    <type fullname="UI.Panel.CyberwarePanel" preserve="all" />
    <type fullname="UI.Panel.DeathPanel" preserve="all" />
    <type fullname="UI.Panel.DiyPanel" preserve="all" />
    <type fullname="UI.Panel.DungeonEntryPanel" preserve="all" />
    <type fullname="UI.Panel.DungeonResultPanel" preserve="all" />
    <type fullname="UI.Panel.EmotionPanel" preserve="all" />
    <type fullname="UI.Panel.EnhanceCenterPanel" preserve="all" />
    <type fullname="UI.Panel.EnhancePanel" preserve="all" />
    <type fullname="UI.Panel.GamemodeSelectPanel" preserve="all" />
    <type fullname="UI.Panel.GuidePanel" preserve="all" />
    <type fullname="UI.Panel.HagenithStorageCenterPanel" preserve="all" />
    <type fullname="UI.Panel.HousingUpgradePanel" preserve="all" />
    <type fullname="UI.Panel.ImagePanel" preserve="all" />
    <type fullname="UI.Panel.InteractionList" preserve="all" />
    <type fullname="UI.Panel.InteractionListItem" preserve="all" />
    <type fullname="UI.Panel.InventoryPanel" preserve="all" />
    <type fullname="UI.Panel.ItemPanel" preserve="all" />
    <type fullname="UI.Panel.JournalPanel" preserve="all" />
    <type fullname="UI.Panel.KeyGuidePanel" preserve="all" />
    <type fullname="UI.Panel.KukulCeremony" preserve="all" />
    <type fullname="UI.Panel.KukulCoinTradeCheckPanel" preserve="all" />
    <type fullname="UI.Panel.KukulPanel" preserve="all" />
    <type fullname="UI.Panel.KukulTempleResultPanel" preserve="all" />
    <type fullname="UI.Panel.MemoryCutScenePanel" preserve="all" />
    <type fullname="UI.Panel.MemoryPanel" preserve="all" />
    <type fullname="UI.Panel.MenuPanel" preserve="all" />
    <type fullname="UI.Panel.Mixxing" preserve="all" />
    <type fullname="UI.Panel.NewEmotionPanel" preserve="all" />
    <type fullname="UI.Panel.OptionPanel" preserve="all" />
    <type fullname="UI.Panel.PrideCheckPanel" preserve="all" />
    <type fullname="UI.Panel.PridePanel" preserve="all" />
    <type fullname="UI.Panel.ProfilePanel" preserve="all" />
    <type fullname="UI.Panel.SaveLoadPanel" preserve="all" />
    <type fullname="UI.Panel.ScenemodeSelectPanel" preserve="all" />
    <type fullname="UI.Panel.SelectSceneMenuPanel" preserve="all" />
    <type fullname="UI.Panel.SellPanel" preserve="all" />
    <type fullname="UI.Panel.ShopPanel" preserve="all" />
    <type fullname="UI.Panel.StartpointSelectPanel" preserve="all" />
    <type fullname="UI.Panel.StorageCenterPanel" preserve="all" />
    <type fullname="UI.Panel.StoragePanel" preserve="all" />
    <type fullname="UI.Panel.SystemPanel" preserve="all" />
    <type fullname="UI.Panel.TabPanel" preserve="all" />
    <type fullname="UI.Panel.TabPanelItem" preserve="all" />
    <type fullname="UI.Panel.TextPanel" preserve="all" />
    <type fullname="UI.Panel.TitleOptionPanel" preserve="all" />
    <type fullname="UI.Panel.TradeCheckPanel" preserve="all" />
    <type fullname="UI.Panel.TrialPanel" preserve="all" />
    <type fullname="UI.Panel.VespaMix" preserve="all" />
    <type fullname="UI.Panel.VespaPanel" preserve="all" />
    <type fullname="UI.PlayerInfo" preserve="all" />
    <type fullname="UI.ProfileSlot" preserve="all" />
    <type fullname="UI.SceneViewButton" preserve="all" />
    <type fullname="UI.SelectIndicator" preserve="all" />
    <type fullname="UI.Shop" preserve="all" />
    <type fullname="UI.SteinSpecial" preserve="all" />
    <type fullname="UI.StorageIcon" preserve="all" />
    <type fullname="UI.StorageList" preserve="all" />
    <type fullname="UI.Storaging" preserve="all" />
    <type fullname="UI.VespaIngredientMix" preserve="all" />
    <type fullname="UI.Wallet" preserve="all" />
    <type fullname="UIStyles" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitterWithMax" preserve="all" />
    <type fullname="Util.EffectPlayer" preserve="all" />
    <type fullname="Util.Follow" preserve="all" />
    <type fullname="Util.MainCameraFollow" preserve="all" />
    <type fullname="VolumetricFogAndMist2.VolumetricFog" preserve="all" />
    <type fullname="VolumetricFogAndMist2.VolumetricFogProfile" preserve="all" />
    <type fullname="VolumetricLights.VolumetricLight" preserve="all" />
    <type fullname="Buff.Interaction.AnxietyInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.GreedInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.HopeAndDespairInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.JoyAndSorrowInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.LoveAndLonelinessInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.MemoryInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.PrideInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.RageInteraction" preserve="nothing" serialized="true" />
    <type fullname="Buff.Interaction.TrialInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.AlbusInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.ApisCraftingInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.ApisInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.CharacterBehaviour" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.CharacterStats" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.ClovisInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.DaynaInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.Elon19Interaction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.Elon29Interaction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.ElonInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.HagenithInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.HelenaInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.HotSpringInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.KukulInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.MeiCountertopInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.MeiInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.MeiPotCountertopInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.MoveToController" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.NPCInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.NikolInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.NoctusInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.PumpkinInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.RagnarInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.SecurityProtocolInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.SteinInteraction" preserve="nothing" serialized="true" />
    <type fullname="Characters.Components.VespaInteraction" preserve="nothing" serialized="true" />
    <type fullname="ECparticleColorChangerMaster/colorChange" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.BedLiedownInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.BombBoxDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.BreakableFieldObjectStats" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.FarmDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.HoneyCombDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.PlantDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.ResourcableFieldObjectBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.ResourcableFieldObjectStats" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.RockDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.Components.TreeDamagedBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.DungeonEntryInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.DungeonRewardInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.GetItemInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.GetQuestItemInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.HpRecoveryInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.HpRecoveryInteraction30" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.KukulTempleRewardInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.LampToggleInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.SchemaBossInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.TreeRemoveInteraction" preserve="nothing" serialized="true" />
    <type fullname="FieldObjects.WarpInteraction" preserve="nothing" serialized="true" />
    <type fullname="GameObjectToggleEvent/ToggleEvent" preserve="nothing" serialized="true" />
    <type fullname="PositionBehaviour" preserve="nothing" serialized="true" />
    <type fullname="ScreenFaderBehaviour" preserve="nothing" serialized="true" />
    <type fullname="StyleSpriteData" preserve="nothing" serialized="true" />
    <type fullname="StyleTintData" preserve="nothing" serialized="true" />
    <type fullname="StyleToggleData" preserve="nothing" serialized="true" />
    <type fullname="TGGameEngine.TGDoor/TGDoorItem" preserve="nothing" serialized="true" />
    <type fullname="UI.DaynaSpecial/DaynaSpecialSlot" preserve="nothing" serialized="true" />
    <type fullname="UI.MemoryCutSceneManager/SetMain" preserve="nothing" serialized="true" />
    <type fullname="UI.MemoryCutSceneManager/SetSceneButton" preserve="nothing" serialized="true" />
    <type fullname="UI.Panel.SetMainButtonData" preserve="nothing" serialized="true" />
    <type fullname="UI.Panel.SetNPCButtonData" preserve="nothing" serialized="true" />
    <type fullname="UI.SteinSpecial/DaynaSpecialSlot" preserve="nothing" serialized="true" />
    <type fullname="UIStyles/UIStyle" preserve="nothing" serialized="true" />
    <type fullname="Util.SerializableGuid" preserve="nothing" serialized="true" />
    <type fullname="Dialogues.Commands.BranchCommand" preserve="nothing" serialized="true" />
    <type fullname="Dialogues.Commands.EndCommand" preserve="nothing" serialized="true" />
    <type fullname="Dialogues.Commands.NpcAnimationCommand" preserve="nothing" serialized="true" />
    <type fullname="Dialogues.Commands.ShopCommand" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Cinemachine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cinemachine.CinemachineBrain" preserve="all" />
    <type fullname="Cinemachine.CinemachineComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineFramingTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachinePipeline" preserve="all" />
    <type fullname="Cinemachine.CinemachineTargetGroup" preserve="all" />
    <type fullname="Cinemachine.CinemachineTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineVirtualCamera" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/BrainEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Elringus.Naninovel.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Naninovel.BacklogMessage" preserve="all" />
    <type fullname="Naninovel.CGGalleryGrid" preserve="all" />
    <type fullname="Naninovel.CGGalleryGridSlot" preserve="all" />
    <type fullname="Naninovel.FX.Animate" preserve="all" />
    <type fullname="Naninovel.FX.DepthOfField" preserve="all" />
    <type fullname="Naninovel.FX.DigitalGlitch" preserve="all" />
    <type fullname="Naninovel.FX.Rain" preserve="all" />
    <type fullname="Naninovel.FX.ShakeBackground" preserve="all" />
    <type fullname="Naninovel.FX.ShakeCamera" preserve="all" />
    <type fullname="Naninovel.FX.ShakeCharacter" preserve="all" />
    <type fullname="Naninovel.FX.ShakePrinter" preserve="all" />
    <type fullname="Naninovel.FX.Snow" preserve="all" />
    <type fullname="Naninovel.FX.SunShafts" preserve="all" />
    <type fullname="Naninovel.GameSettingsPreviewPrinter" preserve="all" />
    <type fullname="Naninovel.LabeledButton" preserve="all" />
    <type fullname="Naninovel.ManagedTextProvider" preserve="all" />
    <type fullname="Naninovel.ReactToAspectRatio" preserve="all" />
    <type fullname="Naninovel.Script" preserve="all" />
    <type fullname="Naninovel.ScriptableButton" preserve="all" />
    <type fullname="Naninovel.ScriptableLabeledButton" preserve="all" />
    <type fullname="Naninovel.TipsListItem" preserve="all" />
    <type fullname="Naninovel.UI.AuthorImage" preserve="all" />
    <type fullname="Naninovel.UI.AuthorNameTMProPanel" preserve="all" />
    <type fullname="Naninovel.UI.AuthorNameUIPanel" preserve="all" />
    <type fullname="Naninovel.UI.BacklogCloseButton" preserve="all" />
    <type fullname="Naninovel.UI.BacklogPanel" preserve="all" />
    <type fullname="Naninovel.UI.CGGalleryPanel" preserve="all" />
    <type fullname="Naninovel.UI.CGGalleryReturnButton" preserve="all" />
    <type fullname="Naninovel.UI.CGViewerPanel" preserve="all" />
    <type fullname="Naninovel.UI.ChatMessage" preserve="all" />
    <type fullname="Naninovel.UI.ChatPrinterPanel" preserve="all" />
    <type fullname="Naninovel.UI.ChoiceHandlerButton" preserve="all" />
    <type fullname="Naninovel.UI.ChoiceHandlerPanel" preserve="all" />
    <type fullname="Naninovel.UI.ClickThroughPanel" preserve="all" />
    <type fullname="Naninovel.UI.ConfirmationPanel" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelAutoPlayButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelHideButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelLoadButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelLogButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelQuickLoadButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelQuickSaveButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelSaveButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelSettingsButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelSkipButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelTipsButton" preserve="all" />
    <type fullname="Naninovel.UI.ControlPanelTitleButton" preserve="all" />
    <type fullname="Naninovel.UI.EngineVersionText" preserve="all" />
    <type fullname="Naninovel.UI.ExternalScriptsBrowserPanel" preserve="all" />
    <type fullname="Naninovel.UI.ExternalScriptsBrowserReturnButton" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsAutoDelaySlider" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsBgmVolumeSlider" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsFontDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsFontSizeDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsGraphicsDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsLanguageDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsMasterVolumeSlider" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsMenu" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsMessageSpeedSlider" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsResolutionDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsReturnButton" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsScreenModeDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsSfxVolumeSlider" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsSkipModeDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsVoiceLocaleDropdown" preserve="all" />
    <type fullname="Naninovel.UI.GameSettingsVoiceVolumeSlider" preserve="all" />
    <type fullname="Naninovel.UI.GameStateSlot" preserve="all" />
    <type fullname="Naninovel.UI.GameStateSlotsGrid" preserve="all" />
    <type fullname="Naninovel.UI.MovieUI" preserve="all" />
    <type fullname="Naninovel.UI.NavigatorPlayButton" preserve="all" />
    <type fullname="Naninovel.UI.PingPongInputIndicator" preserve="all" />
    <type fullname="Naninovel.UI.RevealableTextPrinterPanel" preserve="all" />
    <type fullname="Naninovel.UI.RevealableTMProText" preserve="all" />
    <type fullname="Naninovel.UI.RevealableUIText" preserve="all" />
    <type fullname="Naninovel.UI.RollbackUI" preserve="all" />
    <type fullname="Naninovel.UI.SaveLoadMenu" preserve="all" />
    <type fullname="Naninovel.UI.SaveLoadMenuReturnButton" preserve="all" />
    <type fullname="Naninovel.UI.SceneTransitionUI" preserve="all" />
    <type fullname="Naninovel.UI.TipsPanel" preserve="all" />
    <type fullname="Naninovel.UI.TipsReturnButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleCGGalleryButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleContinueButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleExitButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleExternalScriptsButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleMenu" preserve="all" />
    <type fullname="Naninovel.UI.TitleNewGameButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleSettingsButton" preserve="all" />
    <type fullname="Naninovel.UI.TitleTipsButton" preserve="all" />
    <type fullname="Naninovel.UI.TypingIndicator" preserve="all" />
    <type fullname="Naninovel.UI.VariableInputPanel" preserve="all" />
    <type fullname="Naninovel.ColorUnityEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.ManagedTextProvider/ValueChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.ReactToAspectRatio/ThresholdReachedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.StringUnityEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.ChatMessage/MessageTextChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.CustomUI/FontChangeConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.RevealableTextPrinterPanel/AuthorChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.ChoiceHandlerButton/SummaryTextChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.CommandScriptLine" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.AddChoice" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.AppendLineBreak" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.BeginIf" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.ElseIf" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.EndIf" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.Gosub" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.HideAllCharacters" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.InputCustomVariable" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.ModifyCharacter" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.PlaySfx" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.PrintText" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.Return" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.SetCustomVariable" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.Stop" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.Commands.StopSfx" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.CommentScriptLine" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.EmptyScriptLine" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.GenericTextScriptLine" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.LabelScriptLine" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.BacklogMessage/OnAuthorChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.BacklogMessage/OnMessageChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.OnGridPageChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.ResourceLoaderConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.ScriptableGridSlot/OnSlotClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.TipsListItem/OnLabelChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.TipsListItem/OnLabelStyleChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.ConfirmationPanel/OnMessageChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.GameStateSlot/OnTitleTextChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="Naninovel.UI.NavigatorPlayButton/OnLabelChangedEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="KinematicCharacterController, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="KinematicCharacterController.KinematicCharacterMotor" preserve="all" />
  </assembly>
  <assembly fullname="MagicaCloth, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="MagicaCloth.ClothData" preserve="all" />
    <type fullname="MagicaCloth.MagicaBoneCloth" preserve="all" />
    <type fullname="MagicaCloth.MagicaBoneSpring" preserve="all" />
    <type fullname="MagicaCloth.MagicaCapsuleCollider" preserve="all" />
    <type fullname="MagicaCloth.MagicaMeshCloth" preserve="all" />
    <type fullname="MagicaCloth.MagicaRenderDeformer" preserve="all" />
    <type fullname="MagicaCloth.MagicaSphereCollider" preserve="all" />
    <type fullname="MagicaCloth.MagicaVirtualDeformer" preserve="all" />
    <type fullname="MagicaCloth.MeshData" preserve="all" />
    <type fullname="MagicaCloth.SelectionData" preserve="all" />
    <type fullname="MagicaCloth.BezierParam" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.BoneClothTarget" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.ClampDistanceConstraint/ClampDistanceData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.ClothParams" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.CompositeRotationConstraint/RootInfo" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.CompositeRotationConstraint/RotationData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.LineWorker/LineRotationData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.LineWorker/LineRotationRootInfo" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.MeshData/ChildData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.MeshData/VertexWeight" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.PenetrationConstraint/PenetrationData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.PhysicsTeamData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.ReferenceDataIndex" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.RenderMeshDeformer" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.RestoreDistanceConstraint/RestoreDistanceData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.SelectionData/DeformerSelection" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.TriangleBendConstraint/TriangleBendData" preserve="nothing" serialized="true" />
    <type fullname="MagicaCloth.VirtualMeshDeformer" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="MeshBakerCore, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="MB3_MeshBaker" preserve="all" />
    <type fullname="DigitalOpus.MB.Core.MB3_MeshCombinerSingle" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="MoreMountains.Feedbacks, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="MoreMountains.Feedbacks.MMF_Player" preserve="all" />
    <type fullname="MoreMountains.Feedbacks.MMF_Graphic" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_GraphicCrossFade" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_HoldingPause" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_Image" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_Pause" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_Position" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_Scale" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_SetActive" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_SpriteRenderer" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMFeedbacksEvents" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="MoreMountains.Tools, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="MoreMountains.Tools.MMBillboard" preserve="all" />
    <type fullname="MoreMountains.Tools.ShaderController" preserve="all" />
    <type fullname="MoreMountains.Tools.MMTweenType" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="SelectableGroup, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UI.SelectableGroup" preserve="all" />
    <type fullname="UI.SelectableGroupElement" preserve="all" />
  </assembly>
  <assembly fullname="ToonyColorsPro.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="ToonyColorsPro.Runtime.TCP2_PlanarReflection" preserve="all" />
  </assembly>
  <assembly fullname="UIEffect, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIEffects.UIFlip" preserve="all" />
    <type fullname="Coffee.UIEffects.UIGradient" preserve="all" />
  </assembly>
  <assembly fullname="Unity.2D.Tilemap.Extras, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.RuleTile" preserve="all" />
    <type fullname="UnityEngine.RuleTile/TilingRule" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.AI.Navigation, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Unity.AI.Navigation.NavMeshLink" preserve="all" />
    <type fullname="Unity.AI.Navigation.NavMeshSurface" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalLightData" preserve="all" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_Dropdown" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.TMP_InputField" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphValueRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Timeline, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Timeline.ActivationPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.ActivationTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.AnimationPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.AnimationTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.AudioPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.AudioTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.GroupTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.SignalReceiver" preserve="all" />
    <type fullname="UnityEngine.Timeline.TimelineAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.AudioClipProperties" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.AudioMixerProperties" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.SignalReceiver/EventKeyValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineClip" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Audio.AudioMixerSnapshotController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AI.NavMeshData" preserve="all" />
    <type fullname="UnityEngine.AI.NavMeshObstacle" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animation" preserve="all" />
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animations.PositionConstraint" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Audio.AudioMixer" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="all" />
    <type fullname="UnityEngine.AudioClip" preserve="all" />
    <type fullname="UnityEngine.AudioListener" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LineRenderer" preserve="all" />
    <type fullname="UnityEngine.LODGroup" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.Rendering.SortingGroup" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Texture3D" preserve="all" />
    <type fullname="UnityEngine.TrailRenderer" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[ICyberwareData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[Items.IItemData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[Journal.IJournalData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.String]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[Triggers.Events.TriggerEvent]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.DirectorModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Playables.PlayableDirector" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.GridModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Grid" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.Physics2DModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider2D" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TilemapModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Tilemaps.Tile" preserve="all" />
    <type fullname="UnityEngine.Tilemaps.Tilemap" preserve="all" />
    <type fullname="UnityEngine.Tilemaps.TilemapRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all" />
    <type fullname="UnityEngine.UI.AspectRatioFitter" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.Dropdown" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.InputField" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/EndEditEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="MoreMountains.Feedbacks.MMTools">
    <type fullname="MoreMountains.Feedbacks.MMF_CanvasGroup" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Feedbacks.MMF_ShaderController" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="MoreMountains.Feedbacks.TextMeshPro">
    <type fullname="MoreMountains.Feedbacks.MMF_TMPAlpha" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sirenix.Serialization">
    <type fullname="Sirenix.Serialization.SerializationData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Mathematics">
    <type fullname="Unity.Mathematics.float3" preserve="nothing" serialized="true" />
    <type fullname="Unity.Mathematics.float4" preserve="nothing" serialized="true" />
    <type fullname="Unity.Mathematics.quaternion" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>