[Physics::Mo<PERSON>le] Initialized MultithreadedJobDispatcher with {0} workers.
Initialize engine version: 2021.3.43f1 (6f9470916942)
[Subsystems] Discovering subsystems at path D:/QuarkNetdiskDownload/PC-P00546/P00546/Gray Haven/Gray Haven_Data/UnitySubsystems
GfxDevice: creating device client; threaded=1; jobified=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     8008 MB
    Driver:   32.0.101.6913
<RI> Initializing input.

New input system (experimental) initialized
Using XInput
<RI> Input initialized.

MaximizedWindow is not currently supported on Windows, setting to FullsceenWindow instead
<RI> Initialized touch support.

Couldn't create a Convex Mesh from source mesh "wheels and structure" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
Couldn't create a Convex Mesh from source mesh "base_low" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
Odin Serializer ArchitectureInfo initialization with defaults (all unaligned read/writes disabled).
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Sirenix.Serialization.GlobalSerializationConfig:get_Logger()
Sirenix.Serialization.BinaryDataReader:EnterArray(Int64&)
Sirenix.Serialization.ListFormatter`1:DeserializeImplementation(List`1&, IDataReader)
Sirenix.Serialization.BaseFormatter`1:Deserialize(IDataReader)
Sirenix.Serialization.AnySerializer:ReadValueWeak(IDataReader)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, IDataReader)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, Byte[]&, List`1&, DataFormat, DeserializationContext)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, SerializationData&, DeserializationContext, Boolean, List`1)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, SerializationData&, DeserializationContext)
Sirenix.OdinInspector.SerializedMonoBehaviour:UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()

UnloadTime: 4.995200 ms
Steam initialized. Logged in as: Goldberg
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Game:Init()

Odin Serializer detected whitelisted runtime platform WindowsPlayer and memory read test succeeded; enabling all unaligned memory read/writes.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Sirenix.Serialization.ArchitectureInfo:SetRuntimePlatform(RuntimePlatform)
Sirenix.Serialization.UnitySerializationInitializer:Initialize()

d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
옵션
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnOption()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

제작진
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnWorker()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

옵션
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnOption()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

제작진
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnWorker()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

옵션
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnOption()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line 1500440360]

The referenced script on this Behaviour (Game Object '!ftraceLightmaps') is missing!
Unloading 6 Unused Serialized files (Serialized files now loaded: 4)
UnloadTime: 36.283400 ms
Unloading 1791 unused Assets to reduce memory usage. Loaded Objects now: 30701.
Total: 42.279900 ms (FindLiveObjects: 1.666200 ms CreateObjectMapping: 1.616200 ms MarkObjects: 30.649400 ms  DeleteObjects: 8.347700 ms)

Exposed name does not exist: Master Volume
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line 1500441128]

Exposed name does not exist: SFX Volume
Naninovel.AudioManager:set_SfxVolume(Single)
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line 1500441064]

Exposed name does not exist: Voice Volume
Naninovel.AudioManager:set_VoiceVolume(Single)
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line 1500441064]

Calling Animator.GotoState on Synchronize layer
Characters.Character:OnModelChanged(GameObject)
Characters.Character:EventSystem.IEventListener.OnEvent(Event)
EventManager:SendImmediately(IEventListener, T)
Characters.Slot:Set(String, Boolean)
Players.Player:Init()
Scenes.<Load>d__15:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[ line 1500441800]

Couldn't create a Convex Mesh from source mesh "base_low" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
NullReferenceException: Object reference not set to an instance of an object.
  at Characters.Character.get_SpawnDirection () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.States.FlyState.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at StateMachine.StateMachine`1[OwnerType].FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Components.CharacterBehaviour.IFixedUpdatable.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Character.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 

Couldn't create a Convex Mesh from source mesh "wheels and structure" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
Unloading 3 Unused Serialized files (Serialized files now loaded: 5)
Unloading 832 unused Assets to reduce memory usage. Loaded Objects now: 516073.
Total: 184.347500 ms (FindLiveObjects: 41.583400 ms CreateObjectMapping: 11.702700 ms MarkObjects: 129.432800 ms  DeleteObjects: 1.628100 ms)

[SetCloth] 의상 세팅 시작: ID = 100003
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.GreyGardenController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[SetCloth] 의상 세팅 완료
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.GreyGardenController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Unloading 2 Unused Serialized files (Serialized files now loaded: 7)
Unloading 3979 unused Assets to reduce memory usage. Loaded Objects now: 514224.
Total: 189.494700 ms (FindLiveObjects: 53.680600 ms CreateObjectMapping: 12.877100 ms MarkObjects: 120.655700 ms  DeleteObjects: 2.280700 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 8)
Unloading 7 unused Assets to reduce memory usage. Loaded Objects now: 514541.
Total: 152.288700 ms (FindLiveObjects: 57.560900 ms CreateObjectMapping: 10.288600 ms MarkObjects: 83.623200 ms  DeleteObjects: 0.815600 ms)

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 공격 실행!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 해제!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 홀드 해제!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

d3d11: multiple uploads in flight for buffer 000002AB90629FA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 000002AB9062BFE0 of size 786432. Falling back to slow path
Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line 1500441208]

Minimum node size must be at least as big as the initial world size. Was: 0.5 Adjusted to: 0
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
BoundsOctree`1:.ctor(Single, Vector3, Single, Single)
Stages.<Init>d__59:MoveNext()
Stages.<AddField>d__14:MoveNext()
Stages.<LoadField>d__13:MoveNext()
Cysharp.Threading.Tasks.UniTaskCompletionSourceCore`1:TrySetResult(TResult)
Cysharp.Threading.Tasks.AsyncOperationHandleConfiguredSource`1:Continuation(AsyncOperationHandle`1)
System.Action`1:Invoke(T)
DelegateList`1:Invoke(T)
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:InvokeCompletionEvent()
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:Complete(TObject, Boolean, Exception, Boolean)
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:Complete(TObject, Boolean, String)
UnityEngine.ResourceManagement.InstanceOperation:Execute()
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:InvokeExecute()
UnityEngine.ResourceManagement.AsyncOperations.<>c__DisplayClass57_0:<add_CompletedTypeless>b__0(AsyncOperationHandle`1)
System.Action`1:Invoke(T)
DelegateList`1:Invoke(T)
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:InvokeCompletionEvent()
UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationBase`1:Complete(TObject, Boolean, Exception, Boolean)
UnityEngine.ResourceManagement.AsyncOperations.ProviderOperation`1:ProviderCompleted(T, Boolean, Exception)
UnityEngine.ResourceManagement.ResourceProviders.ProvideHandle:Complete(T, Boolean, Exception)
UnityEngine.ResourceManagement.ResourceProviders.InternalOp:ActionComplete(AsyncOperation)
UnityEngine.AsyncOperation:InvokeCompletionEvent()

Unloading 2 Unused Serialized files (Serialized files now loaded: 8)
[SetCloth] 의상 세팅 시작: ID = 100004
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.ThreeWaysController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[SetCloth] ClothChangeMap에 ID 100004 없음
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.ThreeWaysController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

Unloading 158863 unused Assets to reduce memory usage. Loaded Objects now: 185405.
Total: 184.944300 ms (FindLiveObjects: 41.892000 ms CreateObjectMapping: 11.919900 ms MarkObjects: 83.033300 ms  DeleteObjects: 48.098400 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 8)
Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 185697.
Total: 99.028900 ms (FindLiveObjects: 25.296100 ms CreateObjectMapping: 7.594900 ms MarkObjects: 65.876400 ms  DeleteObjects: 0.261000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 8)
Unloading 5 unused Assets to reduce memory usage. Loaded Objects now: 185418.
Total: 103.273300 ms (FindLiveObjects: 19.912300 ms CreateObjectMapping: 5.046200 ms MarkObjects: 77.918500 ms  DeleteObjects: 0.395900 ms)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

NullReferenceException: Object reference not set to an instance of an object.
  at UnityEngine.InputSystem.PlayerInput.get_actions () [0x00000] in <00000000000000000000000000000000>:0 
  at Players.Input.InputActionDispatcher.UpdateActionMap () [0x00000] in <00000000000000000000000000000000>:0 
  at Players.Input.InputActionDispatcher.Remove (Players.Input.IInputActionBehaviour behaviour) [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Components.ManualCharacterController.OnDetach () [0x00000] in <00000000000000000000000000000000>:0 
  at FieldObjects.Components.IComponent.DetachFrom (FieldObjects.IFieldObject fo) [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Character.OnDestroy () [0x00000] in <00000000000000000000000000000000>:0 

Memory Statistics:
[ALLOC_TEMP_TLS] TLS Allocator
  StackAllocators : 
    [ALLOC_TEMP_MAIN]
      Peak usage frame count: [4.0 KB-8.0 KB]: 332 frames, [8.0 KB-16.0 KB]: 305 frames, [16.0 KB-32.0 KB]: 6043 frames, [32.0 KB-64.0 KB]: 370 frames, [64.0 KB-128.0 KB]: 3519 frames, [128.0 KB-256.0 KB]: 11 frames, [256.0 KB-0.5 MB]: 7 frames, [0.5 MB-1.0 MB]: 5763 frames, [1.0 MB-2.0 MB]: 2147 frames, [2.0 MB-4.0 MB]: 1 frames, [4.0 MB-8.0 MB]: 9 frames
      Initial Block Size 4.0 MB
      Current Block Size 8.0 MB
      Peak Allocated Bytes 7.9 MB
      Overflow Count 43578
    [ALLOC_TEMP_Loading.AsyncRead]
      Initial Block Size 64.0 KB
      Current Block Size 124.0 KB
      Peak Allocated Bytes 120.1 KB
      Overflow Count 0
    [ALLOC_TEMP_Loading.PreloadManager]
      Initial Block Size 256.0 KB
      Current Block Size 0.5 MB
      Peak Allocated Bytes 511.6 KB
      Overflow Count 153627
    [ALLOC_TEMP_Background Job.Worker 8]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 6]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 0]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.2 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 10]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 9]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 34.4 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 5]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 14]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 6]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 4]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 14]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.0 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 3]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.1 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 13]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 37.8 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 12]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 1
    [ALLOC_TEMP_EnlightenWorker] x 8
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 15]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 1
    [ALLOC_TEMP_Background Job.Worker 1]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 2]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 2
    [ALLOC_TEMP_Background Job.Worker 7]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_UnityGfxDeviceWorker]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 46.3 KB
      Overflow Count 1
    [ALLOC_TEMP_AssetGarbageCollectorHelper] x 15
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 5]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 9]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 37.7 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 13]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 8]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 7]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.2 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 1]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 2]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 38.3 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 3]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 35.4 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 11]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 10]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 0]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 0
    [ALLOC_TEMP_HIDInput]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 10.8 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 11]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 39.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 12]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.0 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 4]
      Initial Block Size 32.0 KB
      Current Block Size 48.0 KB
      Peak Allocated Bytes 45.4 KB
      Overflow Count 0
    [ALLOC_TEMP_BatchDeleteObjects]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
[ALLOC_DEFAULT] Dual Thread Allocator
  Peak main deferred allocation count 261328
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 2767712
        32B: 21 Subsections = 10752 buckets. Failed count: 1311019
        48B: 27 Subsections = 9216 buckets. Failed count: 825676
        64B: 128 Subsections = 32768 buckets. Failed count: 4832846
        80B: 46 Subsections = 9420 buckets. Failed count: 184040
        96B: 10 Subsections = 1706 buckets. Failed count: 158206
        112B: 10 Subsections = 1462 buckets. Failed count: 77655
        128B: 7 Subsections = 896 buckets. Failed count: 468411
    [ALLOC_DEFAULT_MAIN]
      Peak usage frame count: [32.0 MB-64.0 MB]: 11789 frames, [64.0 MB-128.0 MB]: 121 frames, [128.0 MB-256.0 MB]: 165 frames, [256.0 MB-0.50 GB]: 6432 frames
      Requested Block Size 16.0 MB
      Peak Block count 33
      Peak Allocated memory 470.9 MB
      Peak Large allocation bytes 48.4 MB
    [ALLOC_DEFAULT_THREAD]
      Peak usage frame count: [64.0 MB-128.0 MB]: 11640 frames, [128.0 MB-256.0 MB]: 6867 frames
      Requested Block Size 16.0 MB
      Peak Block count 12
      Peak Allocated memory 252.6 MB
      Peak Large allocation bytes 100.5 MB
[ALLOC_TEMP_JOB_1_FRAME]
  Initial Block Size 2.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_2_FRAMES]
  Initial Block Size 2.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_4_FRAMES (JobTemp)]
  Initial Block Size 2.0 MB
  Used Block Count 26
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_ASYNC (Background)]
  Initial Block Size 1.0 MB
  Used Block Count 29
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_GFX] Dual Thread Allocator
  Peak main deferred allocation count 121
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 2767712
        32B: 21 Subsections = 10752 buckets. Failed count: 1311019
        48B: 27 Subsections = 9216 buckets. Failed count: 825676
        64B: 128 Subsections = 32768 buckets. Failed count: 4832846
        80B: 46 Subsections = 9420 buckets. Failed count: 184040
        96B: 10 Subsections = 1706 buckets. Failed count: 158206
        112B: 10 Subsections = 1462 buckets. Failed count: 77655
        128B: 7 Subsections = 896 buckets. Failed count: 468411
    [ALLOC_GFX_MAIN]
      Peak usage frame count: [16.0 MB-32.0 MB]: 6805 frames, [32.0 MB-64.0 MB]: 11702 frames
      Requested Block Size 16.0 MB
      Peak Block count 3
      Peak Allocated memory 37.9 MB
      Peak Large allocation bytes 0 B
    [ALLOC_GFX_THREAD]
      Peak usage frame count: [256.0 MB-0.50 GB]: 2298 frames, [0.50 GB-1.00 GB]: 11379 frames, [1.00 GB-2.00 GB]: 4830 frames
      Requested Block Size 16.0 MB
      Peak Block count 21
      Peak Allocated memory 1.29 GB
      Peak Large allocation bytes 0.97 GB
[ALLOC_CACHEOBJECTS] Dual Thread Allocator
  Peak main deferred allocation count 5203
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 2767712
        32B: 21 Subsections = 10752 buckets. Failed count: 1311019
        48B: 27 Subsections = 9216 buckets. Failed count: 825676
        64B: 128 Subsections = 32768 buckets. Failed count: 4832846
        80B: 46 Subsections = 9420 buckets. Failed count: 184040
        96B: 10 Subsections = 1706 buckets. Failed count: 158206
        112B: 10 Subsections = 1462 buckets. Failed count: 77655
        128B: 7 Subsections = 896 buckets. Failed count: 468411
    [ALLOC_CACHEOBJECTS_MAIN]
      Peak usage frame count: [16.0 MB-32.0 MB]: 811 frames, [32.0 MB-64.0 MB]: 17696 frames
      Requested Block Size 4.0 MB
      Peak Block count 6
      Peak Allocated memory 56.7 MB
      Peak Large allocation bytes 35.6 MB
    [ALLOC_CACHEOBJECTS_THREAD]
      Peak usage frame count: [64.0 MB-128.0 MB]: 1486 frames, [128.0 MB-256.0 MB]: 12 frames, [256.0 MB-0.50 GB]: 75 frames, [0.50 GB-1.00 GB]: 10818 frames, [1.00 GB-2.00 GB]: 6116 frames
      Requested Block Size 4.0 MB
      Peak Block count 244
      Peak Allocated memory 1.23 GB
      Peak Large allocation bytes 0.51 GB
[ALLOC_TYPETREE] Dual Thread Allocator
  Peak main deferred allocation count 0
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 2767712
        32B: 21 Subsections = 10752 buckets. Failed count: 1311019
        48B: 27 Subsections = 9216 buckets. Failed count: 825676
        64B: 128 Subsections = 32768 buckets. Failed count: 4832846
        80B: 46 Subsections = 9420 buckets. Failed count: 184040
        96B: 10 Subsections = 1706 buckets. Failed count: 158206
        112B: 10 Subsections = 1462 buckets. Failed count: 77655
        128B: 7 Subsections = 896 buckets. Failed count: 468411
    [ALLOC_TYPETREE_MAIN]
      Peak usage frame count: [16.0 KB-32.0 KB]: 18507 frames
      Requested Block Size 2.0 MB
      Peak Block count 1
      Peak Allocated memory 32.2 KB
      Peak Large allocation bytes 0 B
    [ALLOC_TYPETREE_THREAD]
      Peak usage frame count: [32.0 KB-64.0 KB]: 2 frames, [128.0 KB-256.0 KB]: 1488 frames, [1.0 MB-2.0 MB]: 10796 frames, [2.0 MB-4.0 MB]: 6221 frames
      Requested Block Size 2.0 MB
      Peak Block count 3
      Peak Allocated memory 4.0 MB
      Peak Large allocation bytes 0 B
