[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Initialize engine version: 2021.3.43f1 (6f9470916942)
[Subsystems] Discovering subsystems at path D:/QuarkNetdiskDownload/PC-P00546/P00546/Gray Haven/Gray Haven_Data/UnitySubsystems
GfxDevice: creating device client; threaded=1; jobified=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     8008 MB
    Driver:   32.0.101.6913
<RI> Initializing input.

New input system (experimental) initialized
Using XInput
<RI> Input initialized.

<RI> Initialized touch support.

Couldn't create a Convex Mesh from source mesh "wheels and structure" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
Couldn't create a Convex Mesh from source mesh "base_low" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
Odin Serializer ArchitectureInfo initialization with defaults (all unaligned read/writes disabled).
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Sirenix.Serialization.GlobalSerializationConfig:get_Logger()
Sirenix.Serialization.BinaryDataReader:EnterArray(Int64&)
Sirenix.Serialization.ListFormatter`1:DeserializeImplementation(List`1&, IDataReader)
Sirenix.Serialization.BaseFormatter`1:Deserialize(IDataReader)
Sirenix.Serialization.AnySerializer:ReadValueWeak(IDataReader)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, IDataReader)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, Byte[]&, List`1&, DataFormat, DeserializationContext)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, SerializationData&, DeserializationContext, Boolean, List`1)
Sirenix.Serialization.UnitySerializationUtility:DeserializeUnityObject(Object, SerializationData&, DeserializationContext)
Sirenix.OdinInspector.SerializedMonoBehaviour:UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()

UnloadTime: 0.216200 ms
Steam initialized. Logged in as: Goldberg
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Game:Init()

Odin Serializer detected whitelisted runtime platform WindowsPlayer and memory read test succeeded; enabling all unaligned memory read/writes.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Sirenix.Serialization.ArchitectureInfo:SetRuntimePlatform(RuntimePlatform)
Sirenix.Serialization.UnitySerializationInitializer:Initialize()

d3d11: multiple uploads in flight for buffer 0000023AD568FFA0 of size 786432. Falling back to slow path
d3d11: multiple uploads in flight for buffer 0000023C041149A0 of size 786432. Falling back to slow path
옵션
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnOption()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line -1963333688]

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line -1963333688]

게임 소식
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
UI.Panel.MenuPanel:OnSupporters()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

암호화된 데이터 불러오기 실패, 암호화되지 않은 데이터 시도: Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
UI.ProfileSlot:Init()
UI.Panel.<Enter>d__57:MoveNext()
UI.Panel.ProfilePanel:Enter(Context)
<PushPanel>d__15`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__14`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel, T)
UI.Panel.MenuPanel:OpenProfile()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

암호화된 데이터 불러오기 실패, 암호화되지 않은 데이터 시도: Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
UI.ProfileSlot:Init()
UI.Panel.<Enter>d__57:MoveNext()
UI.Panel.ProfilePanel:Enter(Context)
<PushPanel>d__15`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__14`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel, T)
UI.Panel.MenuPanel:OpenProfile()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

암호화된 데이터 불러오기 실패, 암호화되지 않은 데이터 시도: Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
UI.ProfileSlot:Init()
UI.Panel.<Enter>d__57:MoveNext()
UI.Panel.ProfilePanel:Enter(Context)
<PushPanel>d__15`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__14`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel, T)
UI.Panel.MenuPanel:OpenProfile()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

암호화된 데이터 불러오기 실패, 암호화되지 않은 데이터 시도: Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
UI.ProfileSlot:Init()
UI.Panel.<Enter>d__57:MoveNext()
UI.Panel.ProfilePanel:Enter(Context)
<PushPanel>d__15`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__14`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel, T)
UI.Panel.MenuPanel:OpenProfile()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

암호화된 데이터 불러오기 실패, 암호화되지 않은 데이터 시도: Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
UI.ProfileSlot:Init()
UI.Panel.<Enter>d__57:MoveNext()
UI.Panel.ProfilePanel:Enter(Context)
<PushPanel>d__15`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__14`1:MoveNext()
TitleUIManager:PushPanel(IUIPanel, T)
UI.Panel.MenuPanel:OpenProfile()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Can not play a disabled audio source
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

[ line -1963333688]

필터링된 데이터:
{
  "Fields": {
    "100003": {
      "FieldObjects": {
        "c6829a64-8009-41f3-9670-7f225ee14850": {
          "Id": "c6829a64-8009-41f3-9670-7f225ee14850",
          "Coordinate": [
            -5,
            5,
            24
          ],
          "Position": [
            -4.5,
            5.32931470870972,
            24.5
          ],
          "Direction": [
            1,
            0,
            0
          ],
          "Stats": {}
        },
        "2775383a-a9f0-477a-99fe-2a368329f013": {
          "Id": "2775383a-a9f0-477a-99fe-2a368329f013",
          "Coordinate": [
            -25,
            5,
            -1
          ],
          "Position": [
            -24.25,
            5.01000022888184,
            -0.75
          ],
          "Direction": [
            -1,
            0,
            0
          ],
          "Stats": {}
        },
        "fc8d8b87-17a7-4140-aaaf-a32ceec65116": {
          "Id": "fc8d8b87-17a7-4140-aaaf-a32ceec65116",
          "Coordinate": [
            14,
            5,
            -26
          ],
          "Position": [
            14,
            5.01000022888184,
            -26
          ],
          "Direction": [
            0,
            0,
            -1
          ],
          "Stats": {}
        },
        "68bd28e9-fd4c-412b-91ff-53dd7b78c55b": {
          "Id": "68bd28e9-fd4c-412b-91ff-53dd7b78c55b",
          "Coordinate": [
            -0.5,
            0,
            -0.5
          ],
          "Position": [
            0,
            0,
            0
          ],
          "Direction": [
            0,
            0,
            0
          ],
          "Stats": {}
        },
        "0276d0f1-f998-48ca-b9be-19a2e831fdf7": {
          "Id": "0276d0f1-f998-48ca-b9be-19a2e831fdf7",
          "Coordinate": [
            -5.5,
            6,
            7.5
          ],
          "Position": [
            -5.5,
            6,
            7.5
          ],
          "Direction": [
            0,
            0,
            -1
          ]
        },
        "35c11ccb-52b5-4494-a433-e971fa0bbe64": {
          "Id": "35c11ccb-52b5-4494-a433-e971fa0bbe64",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            1,
            5,
            -5.5
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "a299adcf-d96e-49e9-989c-cfd0d6c2eda6": {
          "Id": "a299adcf-d96e-49e9-989c-cfd0d6c2eda6",
          "Coordinate": [
            -3,
            5,
            24.25
          ],
          "Position": [
            -1.5,
            5,
            25.75
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "75f1570d-1f7d-4ab9-948b-4edf7c6b525c": {
          "Id": "75f1570d-1f7d-4ab9-948b-4edf7c6b525c",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            -2,
            5,
            24.7499809265137
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "9c512ea4-9602-4337-9df8-662294570fde": {
          "Id": "9c512ea4-9602-4337-9df8-662294570fde",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            0,
            0,
            0
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "6d53e252-5ed5-4153-8cba-fce262d5a0dc": {
          "Id": "6d53e252-5ed5-4153-8cba-fce262d5a0dc",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            7,
            5,
            24.7499809265137
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "a257d414-d3de-45e7-b848-1851f2a93fbd": {
          "Id": "a257d414-d3de-45e7-b848-1851f2a93fbd",
          "Coordinate": [
            -27,
            5,
            0
          ],
          "Position": [
            -25.5,
            5,
            1.5
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "0412bf7c-1da5-4ae6-8c1d-c83a346c2248": {
          "Id": "0412bf7c-1da5-4ae6-8c1d-c83a346c2248",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            -26,
            5,
            0.25
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "e6e7e556-dc67-40c1-b18a-c802d3479427": {
          "Id": "e6e7e556-dc67-40c1-b18a-c802d3479427",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            0,
            0,
            0
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "0524515c-fb65-4e6a-91e7-4212a476ed9c": {
          "Id": "0524515c-fb65-4e6a-91e7-4212a476ed9c",
          "Coordinate": [
            27.625,
            5,
            -2.375
          ],
          "Position": [
            27.625,
            5,
            -2.375
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "c8cf5df3-d04b-443a-b3bd-8fa71e834cfd": {
          "Id": "c8cf5df3-d04b-443a-b3bd-8fa71e834cfd",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            0,
            0,
            0
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "f921415c-b8ea-4edb-a547-adffc4ba1ffd": {
          "Id": "f921415c-b8ea-4edb-a547-adffc4ba1ffd",
          "Coordinate": [
            10.5,
            5,
            -24.5
          ],
          "Position": [
            12,
            5,
            -23
          ],
          "Direction": [
            0,
            0,
            -1
          ]
        },
        "7e071a16-1ba8-4e42-a350-dc40c9797971": {
          "Id": "7e071a16-1ba8-4e42-a350-dc40c9797971",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            12,
            5,
            -24.0000190734863
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "fa2a5894-338d-483e-9017-920c54250847": {
          "Id": "fa2a5894-338d-483e-9017-920c54250847",
          "Coordinate": [
            -33.5,
            5,
            16.5
          ],
          "Position": [
            -32,
            5,
            18
          ],
          "Direction": [
            0,
            0,
            -1
          ]
        },
        "fef806ad-81a1-4515-8b69-4af09f692cb4": {
          "Id": "fef806ad-81a1-4515-8b69-4af09f692cb4",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            -32,
            5,
            16.8010005950928
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "f3bad867-0bfe-4fcd-a341-abdb377d740d": {
          "Id": "f3bad867-0bfe-4fcd-a341-abdb377d740d",
          "Coordinate": [
            12,
            5,
            30.5
          ],
          "Position": [
            13.5,
            5,
            32
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "7030fd81-5e81-4193-9df7-86b6fe00f387": {
          "Id": "7030fd81-5e81-4193-9df7-86b6fe00f387",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            14.0000095367432,
            5,
            30.9999809265137
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "7c0effb6-7e31-4589-92be-615f19f62c08": {
          "Id": "7c0effb6-7e31-4589-92be-615f19f62c08",
          "Coordinate": [
            -14.5,
            5,
            -23.5
          ],
          "Position": [
            -13,
            5,
            -22
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "6d15a19d-23cb-4b5e-b143-b9cb734c365d": {
          "Id": "6d15a19d-23cb-4b5e-b143-b9cb734c365d",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            -13,
            5,
            -23.5000305175781
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "d745874a-ea64-4076-9cbb-e51c947dee9b": {
          "Id": "d745874a-ea64-4076-9cbb-e51c947dee9b",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            0,
            0,
            0
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "867d83cb-4629-4f2a-b627-be75a60d3c80": {
          "Id": "867d83cb-4629-4f2a-b627-be75a60d3c80",
          "Coordinate": [
            -5.5,
            5,
            -21.5
          ],
          "Position": [
            -5.5,
            5,
            -21.5
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "da6a8f06-0909-4731-87fa-18d53935125f": {
          "Id": "da6a8f06-0909-4731-87fa-18d53935125f",
          "Coordinate": [
            22,
            5,
            -15
          ],
          "Position": [
            22,
            5,
            -15
          ],
          "Direction": [
            0,
            0,
            1
          ]
        },
        "0d9199ea-4ef6-46c5-996d-8fee106aa709": {
          "Id": "0d9199ea-4ef6-46c5-996d-8fee106aa709",
          "Coordinate": [
            0,
            0,
            0
          ],
          "Position": [
            22,
            5,
            -15.9300003051758
          ],
          "Direction": [
            0,
            0,
            1
          ]
        }
      }
    }
  },
  "Player": {
    "Progress": {
      "Prologue": "Intro"
    },
    "EntryFieldId": "100004"
  }
}
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Players.SaveData:CleanFieldStart(String)
Players.SaveData:LoadSaveData(Int32, Int32)
UI.TitlePanel:Continue(Int32)
UnityEngine.Events.InvokableCall`1:Invoke(T1)
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

The referenced script on this Behaviour (Game Object '!ftraceLightmaps') is missing!
Unloading 6 Unused Serialized files (Serialized files now loaded: 4)
UnloadTime: 42.166700 ms
Unloading 1791 unused Assets to reduce memory usage. Loaded Objects now: 30693.
Total: 39.130400 ms (FindLiveObjects: 1.493800 ms CreateObjectMapping: 1.462300 ms MarkObjects: 26.831300 ms  DeleteObjects: 9.342600 ms)

Exposed name does not exist: Master Volume
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line -1963332920]

Exposed name does not exist: SFX Volume
Naninovel.AudioManager:set_SfxVolume(Single)
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line -1963332984]

Exposed name does not exist: Voice Volume
Naninovel.AudioManager:set_VoiceVolume(Single)
Naninovel.AudioManager:LoadServiceStateAsync(SettingsStateMap)
Naninovel.<LoadAllServicesFromStateAsync>d__86`2:MoveNext()
Naninovel.StateManager:LoadAllServicesFromStateAsync(TState)
Naninovel.<<PerformPostEngineInitializationTasks>g__LoadSettingsAsync|91_0>d:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadOrDefaultAsync>d__13:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<LoadAsync>d__12:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<DeserializeDataAsync>d__20:MoveNext()
Naninovel.UniTaskCompletionSource`1:TrySetResult(T)
Naninovel.<ReadTextFileAsync>d__2:MoveNext()
UnityEngine.WorkRequest:Invoke()
UnityEngine.UnitySynchronizationContext:Exec()

[ line -1963332984]

Calling Animator.GotoState on Synchronize layer
Characters.Character:OnModelChanged(GameObject)
Characters.Character:EventSystem.IEventListener.OnEvent(Event)
EventManager:SendImmediately(IEventListener, T)
Characters.Slot:Set(String, Boolean)
Players.Player:Init()
Scenes.<Load>d__15:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[ line -1963332248]

Calling Animator.GotoState on Synchronize layer
Characters.Character:OnModelChanged(GameObject)
Characters.Character:EventSystem.IEventListener.OnEvent(Event)
EventManager:SendImmediately(IEventListener, T)
Scenes.<Load>d__15:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[ line -1963332056]

의상 데이터 로드
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Players.SaveData:Load(Int32, Int32)
Scenes.<Load>d__15:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

Couldn't create a Convex Mesh from source mesh "base_low" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
NullReferenceException: Object reference not set to an instance of an object.
  at Characters.Character.get_SpawnDirection () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.States.FlyState.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at StateMachine.StateMachine`1[OwnerType].FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Components.CharacterBehaviour.IFixedUpdatable.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Character.FixedUpdate () [0x00000] in <00000000000000000000000000000000>:0 

Couldn't create a Convex Mesh from source mesh "wheels and structure" within the maximum polygons limit (256). The partial hull will be used. Consider simplifying your mesh.
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (1)"
BoxCollider does not support negative scale or size.
The effective box size has been forced positive and is likely to give unexpected collision geometry.
If you absolutely need to use negative scaling you can use the convex MeshCollider. Scene hierarchy path "Stage/Greygarden/VespaTown_Palace/Prop/VespaSet_Fence1_4 (2)"
Unloading 3 Unused Serialized files (Serialized files now loaded: 5)
Unloading 1041 unused Assets to reduce memory usage. Loaded Objects now: 515649.
Total: 183.667000 ms (FindLiveObjects: 37.700800 ms CreateObjectMapping: 11.618500 ms MarkObjects: 132.501400 ms  DeleteObjects: 1.845900 ms)

[SetCloth] 의상 세팅 시작: ID = 100003
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.GreyGardenController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

[SetCloth] 의상 세팅 완료
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Quest.NpcManager:SetCloth(Field)
Quest.NpcManager:OnFieldEntryEvent(FieldEntryEvent)
Stages.Fields.Controllers.GreyGardenController:OnFieldEntryEvent(FieldEntryEvent)
Publisher`1:PublishEvent(T)
Publisher`1:OnEvent(Event)
EventManager:Update()
<Update>d__8:MoveNext()
Cysharp.Threading.Tasks.Internal.ContinuationQueue:RunCore()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 공격 시작!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

 연타 공격!
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:Log(Object)
Characters.Components.ManualCharacterController:IFixedUpdatable.FixedUpdate()
Characters.Character:FixedUpdate()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.<Enter>d__28:MoveNext()
UI.Panel.MemoryCutScenePanel:Enter(UIContext)
UI.Panel.<>c:<Enter>b__16_1(ValueTuple`2)
System.Func`2:Invoke(T)
System.Linq.WhereSelectListIterator`2:MoveNext()
Cysharp.Threading.Tasks.Internal.ArrayPoolUtil:Materialize(IEnumerable`1)
Cysharp.Threading.Tasks.UniTask:WhenAll(IEnumerable`1)
UI.Panel.<Enter>d__16:MoveNext()
UI.Panel.TabPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.PlayerHUD:ShowMenu(Int32)
UI.PlayerHUD:OnPlayerInputEvent(PlayerInputEvent)
Publisher`1:PublishEvent(T)
EventManager:PublishImmediately(T)
Characters.Components.ManualCharacterController:Players.Input.IInputActionBehaviour.OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnPrevTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetMainScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.MemoryCutScenePanel:SetNPCScene()
UI.Panel.MemoryCutScenePanel:OnNextTab(CallbackContext&)
Players.Input.InputActionBehaviour:OnActionTriggered(CallbackContext&)
UI.Panel.TabPanel:OnActionTriggered(CallbackContext&)
Players.Input.InputActionDispatcher:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.PlayerInput:OnActionTriggered(CallbackContext)
System.Action`1:Invoke(T)
UnityEngine.InputSystem.Utilities.DelegateHelpers:InvokeCallbacksSafe(CallbackArray`1&, TValue, String, Object)
UnityEngine.InputSystem.InputActionState:CallActionListeners(Int32, InputActionMap, InputActionPhase, CallbackArray`1&, String)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfActionInternal(Int32, TriggerState*, InputActionPhase, TriggerState&)
UnityEngine.InputSystem.InputActionState:ChangePhaseOfAction(InputActionPhase, TriggerState&, InputActionPhase)
UnityEngine.InputSystem.InputActionState:ProcessDefaultInteraction(TriggerState&, Int32)
UnityEngine.InputSystem.InputActionState:ProcessControlStateChange(Int32, Int32, Int32, Double, InputEventPtr)
UnityEngine.InputSystem.InputActionState:UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor.NotifyControlStateChanged(InputControl, Double, InputEventPtr, Int64)
UnityEngine.InputSystem.InputManager:FireStateChangeNotifications(Int32, Double, InputEvent*)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputUpdateType, Void*, UInt32, UInt32, Double, InputEventPtr)
UnityEngine.InputSystem.InputManager:UpdateState(InputDevice, InputEvent*, InputUpdateType)
UnityEngine.InputSystem.InputManager:OnUpdate(InputUpdateType, InputEventBuffer&)
UnityEngine.InputSystem.LowLevel.<>c__DisplayClass7_0:<set_onUpdate>b__0(NativeInputUpdateType, NativeInputEventBuffer*)
UnityEngineInternal.Input.NativeInputSystem:NotifyUpdate(NativeInputUpdateType, IntPtr)

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

Parent of RectTransform is being set with parent property. Consider using the SetParent method instead, with the worldPositionStays argument set to false. This will retain local orientation and scale rather than world orientation and scale, which can prevent common UI scaling issues.
UnityEngine.Logger:Log(LogType, Object, Object)
UnityEngine.Debug:LogWarning(Object, Object)
UnityEngine.Transform:set_parent(Transform)
UI.Panel.SelectSceneMenuPanel:PushButton()
UI.Panel.<Enter>d__34:MoveNext()
UI.Panel.SelectSceneMenuPanel:Enter(Context)
<PushPanel>d__17`1:MoveNext()
UIManager:PushPanel(IUIPanel`1, T)
<PushPanel>d__16`1:MoveNext()
UIManager:PushPanel(IUIPanel, T)
UI.MemoryChapterButton:Push()
UnityEngine.Events.InvokableCall:Invoke()
UnityEngine.Events.UnityEvent:Invoke()
UnityEngine.EventSystems.ExecuteEvents:Execute(GameObject, BaseEventData, EventFunction`1)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointerButton(ButtonState&, PointerEventData)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:ProcessPointer(PointerModel&)
UnityEngine.InputSystem.UI.CustomInputSystemUIInputModule:Process()
UnityEngine.EventSystems.EventSystem:Update()

NullReferenceException: Object reference not set to an instance of an object.
  at TMPro.TMP_SubMeshUI.UpdateMaterial () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.SetMaterialDirty () [0x00000] in <00000000000000000000000000000000>:0 
  at UnityEngine.UI.MaskableGraphic.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 
  at TMPro.TMP_SubMeshUI.OnDisable () [0x00000] in <00000000000000000000000000000000>:0 

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

Destroying object multiple times. Don't use DestroyImmediate on the same object in OnDisable or OnDestroy.
Characters.Components.AICharacterController:OnDetach()
Characters.Components.MoveToController:OnDetach()
FieldObjects.Components.IComponent:DetachFrom(IFieldObject)
Characters.Character:OnDestroy()

[ line -1963340472]

NullReferenceException: Object reference not set to an instance of an object.
  at UnityEngine.InputSystem.PlayerInput.get_actions () [0x00000] in <00000000000000000000000000000000>:0 
  at Players.Input.InputActionDispatcher.UpdateActionMap () [0x00000] in <00000000000000000000000000000000>:0 
  at Players.Input.InputActionDispatcher.Remove (Players.Input.IInputActionBehaviour behaviour) [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Components.ManualCharacterController.OnDetach () [0x00000] in <00000000000000000000000000000000>:0 
  at FieldObjects.Components.IComponent.DetachFrom (FieldObjects.IFieldObject fo) [0x00000] in <00000000000000000000000000000000>:0 
  at Characters.Character.OnDestroy () [0x00000] in <00000000000000000000000000000000>:0 

Memory Statistics:
[ALLOC_TEMP_TLS] TLS Allocator
  StackAllocators : 
    [ALLOC_TEMP_MAIN]
      Peak usage frame count: [4.0 KB-8.0 KB]: 286 frames, [8.0 KB-16.0 KB]: 132 frames, [16.0 KB-32.0 KB]: 919 frames, [32.0 KB-64.0 KB]: 290 frames, [64.0 KB-128.0 KB]: 973 frames, [128.0 KB-256.0 KB]: 8 frames, [256.0 KB-0.5 MB]: 6 frames, [0.5 MB-1.0 MB]: 4782 frames, [1.0 MB-2.0 MB]: 2402 frames, [2.0 MB-4.0 MB]: 1 frames, [4.0 MB-8.0 MB]: 3 frames
      Initial Block Size 4.0 MB
      Current Block Size 8.0 MB
      Peak Allocated Bytes 7.3 MB
      Overflow Count 43502
    [ALLOC_TEMP_Loading.AsyncRead]
      Initial Block Size 64.0 KB
      Current Block Size 124.0 KB
      Peak Allocated Bytes 120.1 KB
      Overflow Count 0
    [ALLOC_TEMP_Loading.PreloadManager]
      Initial Block Size 256.0 KB
      Current Block Size 0.5 MB
      Peak Allocated Bytes 511.3 KB
      Overflow Count 151106
    [ALLOC_TEMP_Background Job.Worker 8]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 35.4 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 6]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 28.7 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 0]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 10]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 9]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 5]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.6 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 14]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 6]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 4]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 14]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 20.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 3]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 13]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 12]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_EnlightenWorker] x 8
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 15]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 1]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 2]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 7]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_UnityGfxDeviceWorker]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 45.0 KB
      Overflow Count 0
    [ALLOC_TEMP_AssetGarbageCollectorHelper] x 15
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 5]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 9]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 20.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 13]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 8]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 20.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 7]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 1]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 40.2 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 2]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 35.7 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 3]
      Initial Block Size 32.0 KB
      Current Block Size 48.0 KB
      Peak Allocated Bytes 45.4 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 11]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 10]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 0]
      Initial Block Size 32.0 KB
      Current Block Size 36.0 KB
      Peak Allocated Bytes 32.0 KB
      Overflow Count 2
    [ALLOC_TEMP_HIDInput]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 10.8 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 11]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 12]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 36.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 4]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_BatchDeleteObjects]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
[ALLOC_DEFAULT] Dual Thread Allocator
  Peak main deferred allocation count 225582
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 1648398
        32B: 21 Subsections = 10752 buckets. Failed count: 821034
        48B: 27 Subsections = 9216 buckets. Failed count: 491056
        64B: 128 Subsections = 32768 buckets. Failed count: 4775179
        80B: 46 Subsections = 9420 buckets. Failed count: 114816
        96B: 10 Subsections = 1706 buckets. Failed count: 116391
        112B: 10 Subsections = 1462 buckets. Failed count: 58439
        128B: 7 Subsections = 896 buckets. Failed count: 411861
    [ALLOC_DEFAULT_MAIN]
      Peak usage frame count: [32.0 MB-64.0 MB]: 5673 frames, [64.0 MB-128.0 MB]: 127 frames, [128.0 MB-256.0 MB]: 168 frames, [256.0 MB-0.50 GB]: 3834 frames
      Requested Block Size 16.0 MB
      Peak Block count 32
      Peak Allocated memory 466.6 MB
      Peak Large allocation bytes 48.4 MB
    [ALLOC_DEFAULT_THREAD]
      Peak usage frame count: [64.0 MB-128.0 MB]: 5534 frames, [128.0 MB-256.0 MB]: 4268 frames
      Requested Block Size 16.0 MB
      Peak Block count 12
      Peak Allocated memory 250.9 MB
      Peak Large allocation bytes 100.5 MB
[ALLOC_TEMP_JOB_1_FRAME]
  Initial Block Size 2.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_2_FRAMES]
  Initial Block Size 2.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_4_FRAMES (JobTemp)]
  Initial Block Size 2.0 MB
  Used Block Count 25
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_ASYNC (Background)]
  Initial Block Size 1.0 MB
  Used Block Count 29
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_GFX] Dual Thread Allocator
  Peak main deferred allocation count 53
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 1648398
        32B: 21 Subsections = 10752 buckets. Failed count: 821034
        48B: 27 Subsections = 9216 buckets. Failed count: 491056
        64B: 128 Subsections = 32768 buckets. Failed count: 4775179
        80B: 46 Subsections = 9420 buckets. Failed count: 114816
        96B: 10 Subsections = 1706 buckets. Failed count: 116391
        112B: 10 Subsections = 1462 buckets. Failed count: 58439
        128B: 7 Subsections = 896 buckets. Failed count: 411861
    [ALLOC_GFX_MAIN]
      Peak usage frame count: [16.0 MB-32.0 MB]: 4209 frames, [32.0 MB-64.0 MB]: 5593 frames
      Requested Block Size 16.0 MB
      Peak Block count 3
      Peak Allocated memory 37.9 MB
      Peak Large allocation bytes 0 B
    [ALLOC_GFX_THREAD]
      Peak usage frame count: [256.0 MB-0.50 GB]: 6036 frames, [0.50 GB-1.00 GB]: 3766 frames
      Requested Block Size 16.0 MB
      Peak Block count 21
      Peak Allocated memory 0.93 GB
      Peak Large allocation bytes 0.62 GB
[ALLOC_CACHEOBJECTS] Dual Thread Allocator
  Peak main deferred allocation count 5203
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 1648398
        32B: 21 Subsections = 10752 buckets. Failed count: 821034
        48B: 27 Subsections = 9216 buckets. Failed count: 491056
        64B: 128 Subsections = 32768 buckets. Failed count: 4775179
        80B: 46 Subsections = 9420 buckets. Failed count: 114816
        96B: 10 Subsections = 1706 buckets. Failed count: 116391
        112B: 10 Subsections = 1462 buckets. Failed count: 58439
        128B: 7 Subsections = 896 buckets. Failed count: 411861
    [ALLOC_CACHEOBJECTS_MAIN]
      Peak usage frame count: [16.0 MB-32.0 MB]: 813 frames, [32.0 MB-64.0 MB]: 8989 frames
      Requested Block Size 4.0 MB
      Peak Block count 5
      Peak Allocated memory 49.6 MB
      Peak Large allocation bytes 32.9 MB
    [ALLOC_CACHEOBJECTS_THREAD]
      Peak usage frame count: [64.0 MB-128.0 MB]: 3685 frames, [128.0 MB-256.0 MB]: 21 frames, [256.0 MB-0.50 GB]: 152 frames, [0.50 GB-1.00 GB]: 2428 frames, [1.00 GB-2.00 GB]: 3516 frames
      Requested Block Size 4.0 MB
      Peak Block count 244
      Peak Allocated memory 1.21 GB
      Peak Large allocation bytes 488.2 MB
[ALLOC_TYPETREE] Dual Thread Allocator
  Peak main deferred allocation count 0
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 4.0 MB
      Failed Allocations. Bucket layout:
        16B: 7 Subsections = 7168 buckets. Failed count: 1648398
        32B: 21 Subsections = 10752 buckets. Failed count: 821034
        48B: 27 Subsections = 9216 buckets. Failed count: 491056
        64B: 128 Subsections = 32768 buckets. Failed count: 4775179
        80B: 46 Subsections = 9420 buckets. Failed count: 114816
        96B: 10 Subsections = 1706 buckets. Failed count: 116391
        112B: 10 Subsections = 1462 buckets. Failed count: 58439
        128B: 7 Subsections = 896 buckets. Failed count: 411861
    [ALLOC_TYPETREE_MAIN]
      Peak usage frame count: [16.0 KB-32.0 KB]: 9802 frames
      Requested Block Size 2.0 MB
      Peak Block count 1
      Peak Allocated memory 32.2 KB
      Peak Large allocation bytes 0 B
    [ALLOC_TYPETREE_THREAD]
      Peak usage frame count: [32.0 KB-64.0 KB]: 2 frames, [128.0 KB-256.0 KB]: 3690 frames, [1.0 MB-2.0 MB]: 2488 frames, [2.0 MB-4.0 MB]: 3622 frames
      Requested Block Size 2.0 MB
      Peak Block count 2
      Peak Allocated memory 3.2 MB
      Peak Large allocation bytes 0 B
